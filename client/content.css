/* B端智能助手-APA 样式文件 */

/* 悬浮图标样式 */
.apa-floating-icon {
  position: fixed !important;
  top: 50% !important;
  right: 20px !important;
  width: 60px !important;
  height: 60px !important;
  background: linear-gradient(135deg, #4CAF50, #45a049) !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 24px !important;
  cursor: pointer !important;
  z-index: 10000 !important;
  box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;
  transition: all 0.3s ease !important;
  border: none !important;
  outline: none !important;
}

.apa-floating-icon:hover {
  transform: scale(1.1) !important;
  box-shadow: 0 6px 16px rgba(0,0,0,0.4) !important;
}

/* 助手容器样式 */
.apa-assistant-container {
  position: fixed !important;
  top: 50% !important;
  right: 100px !important;
  transform: translateY(-50%) !important;
  width: 400px !important;
  height: 600px !important;
  background: white !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0,0,0,0.3) !important;
  z-index: 10001 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-size: 14px !important;
  display: none !important;
  overflow: hidden !important;
}

/* 助手面板样式 */
.apa-assistant-panel {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

/* 头部样式 */
.apa-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 12px 16px !important;
  background: #f8f9fa !important;
  border-bottom: 1px solid #e9ecef !important;
}

.apa-header-left {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
}

.apa-title {
  font-weight: 600 !important;
  color: #333 !important;
}

.apa-login-status {
  font-size: 12px !important;
  padding: 2px 8px !important;
  border-radius: 12px !important;
  background: #e9ecef !important;
  color: #666 !important;
}

.apa-login-status.logged-in {
  background: #d4edda !important;
  color: #155724 !important;
}

.apa-login-status.not-logged-in {
  background: #f8d7da !important;
  color: #721c24 !important;
}

.apa-connection-status {
  font-size: 12px !important;
  padding: 2px 8px !important;
  border-radius: 12px !important;
  background: #f8d7da !important;
  color: #721c24 !important;
}

.apa-close-btn {
  background: none !important;
  border: none !important;
  font-size: 18px !important;
  cursor: pointer !important;
  color: #666 !important;
  padding: 4px !important;
  border-radius: 4px !important;
}

.apa-close-btn:hover {
  background: #e9ecef !important;
}

/* Tab样式 */
.apa-tabs {
  display: flex !important;
  background: #f8f9fa !important;
  border-bottom: 1px solid #e9ecef !important;
}

.apa-tab {
  flex: 1 !important;
  padding: 12px 8px !important;
  background: none !important;
  border: none !important;
  cursor: pointer !important;
  color: #666 !important;
  font-size: 13px !important;
  transition: all 0.2s ease !important;
}

.apa-tab:hover {
  background: #e9ecef !important;
}

.apa-tab.active {
  background: white !important;
  color: #333 !important;
  font-weight: 500 !important;
  border-bottom: 2px solid #4CAF50 !important;
}

/* 内容区域样式 */
.apa-content {
  flex: 1 !important;
  overflow: hidden !important;
}

.apa-tab-content {
  display: none !important;
  height: 100% !important;
  overflow-y: auto !important;
}

.apa-tab-content.active {
  display: block !important;
}

/* 任务面板样式 */
.apa-tasks-panel {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

.apa-task-tabs {
  display: flex !important;
  background: #f8f9fa !important;
  border-bottom: 1px solid #e9ecef !important;
}

.apa-task-tab {
  flex: 1 !important;
  padding: 8px 12px !important;
  background: none !important;
  border: none !important;
  cursor: pointer !important;
  color: #666 !important;
  font-size: 12px !important;
}

.apa-task-tab.active {
  background: white !important;
  color: #333 !important;
  font-weight: 500 !important;
}

.apa-task-content {
  flex: 1 !important;
  overflow-y: auto !important;
}

.apa-task-tab-content {
  display: none !important;
  padding: 16px !important;
}

.apa-task-tab-content.active {
  display: block !important;
}

/* 表单样式 */
.apa-form-group {
  margin-bottom: 16px !important;
}

.apa-form-group label {
  display: block !important;
  margin-bottom: 4px !important;
  font-weight: 500 !important;
  color: #333 !important;
  font-size: 13px !important;
}

.apa-form-group input,
.apa-form-group select,
.apa-form-group textarea {
  width: 100% !important;
  padding: 8px 12px !important;
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  font-size: 13px !important;
  box-sizing: border-box !important;
}

.apa-form-group input:focus,
.apa-form-group select:focus,
.apa-form-group textarea:focus {
  outline: none !important;
  border-color: #4CAF50 !important;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2) !important;
}

/* 按钮样式 */
.apa-btn-primary {
  background: #4CAF50 !important;
  color: white !important;
  border: none !important;
  padding: 8px 16px !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  font-size: 13px !important;
  transition: background 0.2s ease !important;
}

.apa-btn-primary:hover {
  background: #45a049 !important;
}

/* 搜索面板样式 */
.apa-search-panel {
  display: flex !important;
  gap: 8px !important;
  margin-bottom: 16px !important;
  flex-wrap: wrap !important;
}

.apa-search-panel input,
.apa-search-panel select {
  flex: 1 !important;
  min-width: 100px !important;
}

/* 表格样式 */
.apa-task-table {
  overflow-x: auto !important;
}

.apa-task-table table {
  width: 100% !important;
  border-collapse: collapse !important;
  font-size: 12px !important;
}

.apa-task-table th,
.apa-task-table td {
  padding: 8px 4px !important;
  text-align: left !important;
  border-bottom: 1px solid #e9ecef !important;
}

.apa-task-table th {
  background: #f8f9fa !important;
  font-weight: 500 !important;
  color: #333 !important;
}

/* 状态样式 */
.status-pending { color: #856404 !important; }
.status-running { color: #0c5460 !important; }
.status-success { color: #155724 !important; }
.status-failed { color: #721c24 !important; }

/* 聊天面板样式 */
.apa-chat-panel {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

.apa-chat-messages {
  flex: 1 !important;
  padding: 16px !important;
  overflow-y: auto !important;
  background: #f8f9fa !important;
}

.apa-message {
  margin-bottom: 16px !important;
}

.apa-message-content {
  background: white !important;
  padding: 8px 12px !important;
  border-radius: 8px !important;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
  font-size: 13px !important;
  line-height: 1.4 !important;
}

.apa-message-bot .apa-message-content {
  background: #e3f2fd !important;
}

.apa-message-user .apa-message-content {
  background: #4CAF50 !important;
  color: white !important;
  margin-left: 40px !important;
}

.apa-message-time {
  font-size: 11px !important;
  color: #666 !important;
  margin-top: 4px !important;
  text-align: right !important;
}

.apa-chat-input {
  border-top: 1px solid #e9ecef !important;
  background: white !important;
}

.apa-quick-commands {
  padding: 8px 16px !important;
  display: flex !important;
  gap: 8px !important;
  flex-wrap: wrap !important;
}

.apa-quick-cmd {
  background: #f8f9fa !important;
  border: 1px solid #e9ecef !important;
  padding: 4px 8px !important;
  border-radius: 12px !important;
  font-size: 11px !important;
  cursor: pointer !important;
  color: #666 !important;
}

.apa-quick-cmd:hover {
  background: #e9ecef !important;
}

.apa-input-area {
  padding: 12px 16px !important;
  display: flex !important;
  gap: 8px !important;
  align-items: flex-end !important;
}

.apa-input-area textarea {
  flex: 1 !important;
  resize: none !important;
  max-height: 80px !important;
}

/* 设置面板样式 */
.apa-settings-panel {
  padding: 16px !important;
}

.apa-form-group input[type="checkbox"] {
  width: auto !important;
  margin-right: 8px !important;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .apa-assistant-container {
    width: 90vw !important;
    right: 5vw !important;
  }
}
