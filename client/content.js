// B端智能助手-APA 内容脚本
class AssistantContent {
  constructor() {
    this.isAssistantVisible = false;
    this.assistantContainer = null;
    this.connectionStatus = 'disconnected';
    this.loginStatus = 'unknown';
    this.init();
  }

  init() {
    // 监听来自background的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
    });

    // 监听页面消息
    window.addEventListener('message', (event) => {
      if (event.data.type === 'TOGGLE_ASSISTANT') {
        this.toggleAssistant();
      }
    });

    // 检查是否为目标系统
    this.checkIfTargetSystem();

    // 定期检查登录状态
    setInterval(() => this.updateLoginStatus(), 30000);
  }

  // 检查是否为目标系统
  async checkIfTargetSystem() {
    // 这里可以根据域名判断是否为目标系统
    const targetDomains = ['erp.', 'admin.', 'manage.', 'system.'];
    const isTarget = targetDomains.some(domain => window.location.hostname.includes(domain));

    if (isTarget) {
      this.createFloatingIcon();
      this.updateLoginStatus();
    }
  }

  // 创建悬浮图标
  createFloatingIcon() {
    if (document.getElementById('apa-floating-icon')) return;

    const floatingIcon = document.createElement('div');
    floatingIcon.id = 'apa-floating-icon';
    floatingIcon.innerHTML = '🤖';
    floatingIcon.className = 'apa-floating-icon';

    floatingIcon.addEventListener('click', () => {
      this.toggleAssistant();
    });

    document.body.appendChild(floatingIcon);
  }

  // 切换助手显示状态
  toggleAssistant() {
    if (this.isAssistantVisible) {
      this.hideAssistant();
    } else {
      this.showAssistant();
    }
  }

  // 显示助手
  showAssistant() {
    if (!this.assistantContainer) {
      this.createAssistantContainer();
    }

    this.assistantContainer.style.display = 'block';
    this.isAssistantVisible = true;

    // 连接WebSocket
    chrome.runtime.sendMessage({ action: 'connectWebSocket' });
  }

  // 隐藏助手
  hideAssistant() {
    if (this.assistantContainer) {
      this.assistantContainer.style.display = 'none';
    }
    this.isAssistantVisible = false;
  }

  // 创建助手容器
  createAssistantContainer() {
    this.assistantContainer = document.createElement('div');
    this.assistantContainer.id = 'apa-assistant-container';
    this.assistantContainer.className = 'apa-assistant-container';

    this.assistantContainer.innerHTML = this.getAssistantHTML();
    document.body.appendChild(this.assistantContainer);

    this.bindEvents();
    this.updateLoginStatus();
  }

  // 获取助手HTML结构
  getAssistantHTML() {
    return `
      <div class="apa-assistant-panel">
        <div class="apa-header">
          <div class="apa-header-left">
            <span class="apa-title">智能助手</span>
            <span class="apa-login-status" id="apa-login-status">检查中...</span>
          </div>
          <div class="apa-header-right">
            <span class="apa-connection-status" id="apa-connection-status">未连接</span>
            <button class="apa-close-btn" id="apa-close-btn">×</button>
          </div>
        </div>

        <div class="apa-tabs">
          <button class="apa-tab active" data-tab="tasks">辅助任务</button>
          <button class="apa-tab" data-tab="chat">智能对话</button>
          <button class="apa-tab" data-tab="settings">设置</button>
        </div>

        <div class="apa-content">
          <div class="apa-tab-content active" id="apa-tasks-content">
            ${this.getTasksHTML()}
          </div>

          <div class="apa-tab-content" id="apa-chat-content">
            ${this.getChatHTML()}
          </div>

          <div class="apa-tab-content" id="apa-settings-content">
            ${this.getSettingsHTML()}
          </div>
        </div>
      </div>
    `;
  }

  // 获取任务页面HTML
  getTasksHTML() {
    return `
      <div class="apa-tasks-panel">
        <div class="apa-task-tabs">
          <button class="apa-task-tab active" data-task-tab="new">发起新任务</button>
          <button class="apa-task-tab" data-task-tab="list">任务列表</button>
        </div>

        <div class="apa-task-content">
          <div class="apa-task-tab-content active" id="apa-new-task">
            <div class="apa-form-group">
              <label>任务类型:</label>
              <select id="apa-task-type">
                <option value="">请选择任务类型</option>
                <option value="data-import">数据导入</option>
                <option value="batch-operation">批量操作</option>
                <option value="report-generation">报表生成</option>
              </select>
            </div>

            <div class="apa-form-group" id="apa-template-section" style="display:none;">
              <label>模板文件:</label>
              <button id="apa-download-template">下载模板</button>
              <input type="file" id="apa-file-upload" accept=".xlsx,.xls,.csv" />
            </div>

            <button id="apa-submit-task" class="apa-btn-primary">提交任务</button>
          </div>

          <div class="apa-task-tab-content" id="apa-task-list">
            <div class="apa-search-panel">
              <input type="text" id="apa-search-name" placeholder="任务名称" />
              <input type="date" id="apa-search-date" />
              <select id="apa-search-status">
                <option value="">全部状态</option>
                <option value="pending">待执行</option>
                <option value="running">执行中</option>
                <option value="success">成功</option>
                <option value="failed">失败</option>
              </select>
              <button id="apa-search-btn">查询</button>
            </div>

            <div class="apa-task-table">
              <table>
                <thead>
                  <tr>
                    <th>任务编号</th>
                    <th>任务名称</th>
                    <th>任务状态</th>
                    <th>创建时间</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody id="apa-task-tbody">
                  <!-- 任务列表将在这里动态加载 -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  // 获取聊天页面HTML
  getChatHTML() {
    return `
      <div class="apa-chat-panel">
        <div class="apa-chat-messages" id="apa-chat-messages">
          <div class="apa-message apa-message-bot">
            <div class="apa-message-content">
              您好！我是智能助手，有什么可以帮助您的吗？
            </div>
            <div class="apa-message-time">${new Date().toLocaleTimeString()}</div>
          </div>
        </div>

        <div class="apa-chat-input">
          <div class="apa-quick-commands">
            <button class="apa-quick-cmd" data-cmd="查看任务状态">查看任务状态</button>
            <button class="apa-quick-cmd" data-cmd="帮助文档">帮助文档</button>
          </div>

          <div class="apa-input-area">
            <textarea id="apa-chat-input" placeholder="请输入您的问题..." rows="2"></textarea>
            <button id="apa-send-btn" class="apa-btn-primary">发送</button>
          </div>
        </div>
      </div>
    `;
  }

  // 获取设置页面HTML
  getSettingsHTML() {
    return `
      <div class="apa-settings-panel">
        <div class="apa-form-group">
          <label>服务器地址:</label>
          <input type="text" id="apa-server-url" placeholder="ws://localhost:8080" />
        </div>

        <div class="apa-form-group">
          <label>代理站点:</label>
          <select id="apa-target-site">
            <option value="">请选择代理站点</option>
          </select>
        </div>

        <div class="apa-form-group">
          <label>代理站点SSO:</label>
          <input type="text" id="apa-sso-url" placeholder="SSO登录地址" />
        </div>

        <div class="apa-form-group">
          <label>
            <input type="checkbox" id="apa-desktop-notification" />
            桌面通知
          </label>
        </div>

        <div class="apa-form-group">
          <label>
            <input type="checkbox" id="apa-sound-alert" />
            声音提醒
          </label>
        </div>

        <button id="apa-save-settings" class="apa-btn-primary">保存设置</button>
      </div>
    `;
  }

  // 绑定事件
  bindEvents() {
    // 关闭按钮
    document.getElementById('apa-close-btn').addEventListener('click', () => {
      this.hideAssistant();
    });

    // Tab切换
    document.querySelectorAll('.apa-tab').forEach(tab => {
      tab.addEventListener('click', (e) => {
        this.switchTab(e.target.dataset.tab);
      });
    });

    // 任务Tab切换
    document.querySelectorAll('.apa-task-tab').forEach(tab => {
      tab.addEventListener('click', (e) => {
        this.switchTaskTab(e.target.dataset.taskTab);
      });
    });

    // 其他事件绑定
    this.bindTaskEvents();
    this.bindChatEvents();
    this.bindSettingsEvents();
  }

  // 绑定任务相关事件
  bindTaskEvents() {
    // 任务类型选择
    document.getElementById('apa-task-type').addEventListener('change', (e) => {
      const templateSection = document.getElementById('apa-template-section');
      if (e.target.value) {
        templateSection.style.display = 'block';
      } else {
        templateSection.style.display = 'none';
      }
    });

    // 提交任务
    document.getElementById('apa-submit-task').addEventListener('click', () => {
      this.submitTask();
    });

    // 搜索任务
    document.getElementById('apa-search-btn').addEventListener('click', () => {
      this.searchTasks();
    });
  }

  // 绑定聊天相关事件
  bindChatEvents() {
    // 发送消息
    document.getElementById('apa-send-btn').addEventListener('click', () => {
      this.sendMessage();
    });

    // 回车发送
    document.getElementById('apa-chat-input').addEventListener('keypress', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        this.sendMessage();
      }
    });

    // 快捷命令
    document.querySelectorAll('.apa-quick-cmd').forEach(btn => {
      btn.addEventListener('click', (e) => {
        document.getElementById('apa-chat-input').value = e.target.dataset.cmd;
        this.sendMessage();
      });
    });
  }

  // 绑定设置相关事件
  bindSettingsEvents() {
    // 保存设置
    document.getElementById('apa-save-settings').addEventListener('click', () => {
      this.saveSettings();
    });

    // 加载当前设置
    this.loadSettings();
  }

  // 处理消息
  handleMessage(request, sender, sendResponse) {
    switch (request.action) {
      case 'websocketMessage':
        this.handleWebSocketMessage(request.data);
        break;
      case 'showAssistant':
      case 'openAssistant':
        this.showAssistant();
        sendResponse({ success: true });
        break;
    }
  }

  // 处理WebSocket消息
  handleWebSocketMessage(data) {
    if (data.type === 'chat') {
      this.addChatMessage(data.content, 'bot');
    } else if (data.type === 'task_update') {
      this.updateTaskStatus(data);
    }
  }

  // 更新登录状态
  async updateLoginStatus() {
    try {
      const response = await chrome.runtime.sendMessage({ action: 'getLoginStatus' });
      const statusElement = document.getElementById('apa-login-status');

      if (statusElement) {
        if (response.status === 'logged_in') {
          statusElement.textContent = '已登录';
          statusElement.className = 'apa-login-status logged-in';
        } else {
          statusElement.textContent = '未获取到登录态';
          statusElement.className = 'apa-login-status not-logged-in';
          statusElement.style.cursor = 'pointer';
          statusElement.onclick = () => this.redirectToSSO();
        }
      }
    } catch (error) {
      console.error('更新登录状态失败:', error);
    }
  }

  // 重定向到SSO
  redirectToSSO() {
    // 这里应该跳转到配置的SSO地址
    alert('请先登录目标系统');
  }

  // Tab切换
  switchTab(tabName) {
    // 移除所有active类
    document.querySelectorAll('.apa-tab').forEach(tab => tab.classList.remove('active'));
    document.querySelectorAll('.apa-tab-content').forEach(content => content.classList.remove('active'));

    // 添加active类
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    document.getElementById(`apa-${tabName}-content`).classList.add('active');
  }

  // 任务Tab切换
  switchTaskTab(tabName) {
    document.querySelectorAll('.apa-task-tab').forEach(tab => tab.classList.remove('active'));
    document.querySelectorAll('.apa-task-tab-content').forEach(content => content.classList.remove('active'));

    document.querySelector(`[data-task-tab="${tabName}"]`).classList.add('active');
    document.getElementById(`apa-${tabName}-task`).classList.add('active');
  }

  // 提交任务
  async submitTask() {
    const taskType = document.getElementById('apa-task-type').value;
    const fileInput = document.getElementById('apa-file-upload');

    if (!taskType) {
      alert('请选择任务类型');
      return;
    }

    if (!fileInput.files[0]) {
      alert('请选择文件');
      return;
    }

    // 这里应该处理文件上传和任务提交
    const taskData = {
      type: taskType,
      fileName: fileInput.files[0].name,
      timestamp: Date.now()
    };

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'submitTask',
        taskData: taskData
      });

      if (response.success) {
        alert('任务提交成功');
        this.switchTaskTab('list');
        this.searchTasks();
      } else {
        alert('任务提交失败: ' + response.error);
      }
    } catch (error) {
      alert('任务提交失败: ' + error.message);
    }
  }

  // 搜索任务
  searchTasks() {
    // 这里应该从服务端获取任务列表
    const mockTasks = [
      {
        id: 'T001',
        name: '数据导入任务',
        status: 'success',
        createTime: '2024-01-15 10:30:00'
      },
      {
        id: 'T002',
        name: '批量操作任务',
        status: 'running',
        createTime: '2024-01-15 11:00:00'
      }
    ];

    this.renderTaskList(mockTasks);
  }

  // 渲染任务列表
  renderTaskList(tasks) {
    const tbody = document.getElementById('apa-task-tbody');
    tbody.innerHTML = tasks.map(task => `
      <tr>
        <td>${task.id}</td>
        <td>${task.name}</td>
        <td><span class="status-${task.status}">${this.getStatusText(task.status)}</span></td>
        <td>${task.createTime}</td>
        <td><button onclick="viewTaskDetail('${task.id}')">查看详情</button></td>
      </tr>
    `).join('');
  }

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'pending': '待执行',
      'running': '执行中',
      'success': '成功',
      'failed': '失败'
    };
    return statusMap[status] || status;
  }

  // 发送消息
  sendMessage() {
    const input = document.getElementById('apa-chat-input');
    const message = input.value.trim();

    if (!message) return;

    this.addChatMessage(message, 'user');
    input.value = '';

    // 发送到服务端
    chrome.runtime.sendMessage({
      action: 'sendMessage',
      data: {
        type: 'chat',
        content: message,
        timestamp: Date.now()
      }
    });
  }

  // 添加聊天消息
  addChatMessage(content, sender) {
    const messagesContainer = document.getElementById('apa-chat-messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `apa-message apa-message-${sender}`;
    messageDiv.innerHTML = `
      <div class="apa-message-content">${content}</div>
      <div class="apa-message-time">${new Date().toLocaleTimeString()}</div>
    `;

    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
  }

  // 加载设置
  async loadSettings() {
    try {
      const result = await chrome.storage.local.get('settings');
      const settings = result.settings || {};

      document.getElementById('apa-server-url').value = settings.serverUrl || '';
      document.getElementById('apa-desktop-notification').checked = settings.desktopNotification || false;
      document.getElementById('apa-sound-alert').checked = settings.soundAlert || false;
    } catch (error) {
      console.error('加载设置失败:', error);
    }
  }

  // 保存设置
  async saveSettings() {
    const settings = {
      serverUrl: document.getElementById('apa-server-url').value,
      desktopNotification: document.getElementById('apa-desktop-notification').checked,
      soundAlert: document.getElementById('apa-sound-alert').checked
    };

    try {
      await chrome.storage.local.set({ settings });
      alert('设置保存成功');
    } catch (error) {
      alert('设置保存失败: ' + error.message);
    }
  }
}

// 初始化内容脚本
new AssistantContent();
